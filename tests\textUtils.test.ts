import { describe, it, expect } from 'vitest';
import { getTextContext, extractSelectionContext, createAutocompletePrompt, createAnswerSelectionPrompt, extractCursorContext, createCursorInsertPrompt } from '../src/lib/utils/textUtils';

describe('Text Utils', () => {
  describe('getTextContext', () => {
    it('should return full text when no max length specified', () => {
      const text = "Hello world";
      const result = getTextContext(text);
      expect(result).toBe("Hello world");
    });

    it('should truncate text when max length specified', () => {
      const text = "Hello world this is a long text";
      const result = getTextContext(text, 10);
      expect(result).toBe(" long text");
    });

    it('should return full text when max length is longer than text', () => {
      const text = "Short";
      const result = getTextContext(text, 100);
      expect(result).toBe("Short");
    });
  });

// Test extractSelectionContext
test('extractSelectionContext - basic case', () => {
  const fullText = "The quick brown fox jumps over the lazy dog";
  const result = extractSelectionContext(fullText, 10, 15, 5, 5);
  
  assertEqual(result.selectedText, "brown");
  assertEqual(result.beforeText, "quick");
  assertEqual(result.afterText, " fox ");
});

test('extractSelectionContext - at beginning', () => {
  const fullText = "Hello world";
  const result = extractSelectionContext(fullText, 0, 5, 10, 3);
  
  assertEqual(result.selectedText, "Hello");
  assertEqual(result.beforeText, "");
  assertEqual(result.afterText, " wo");
});

test('extractSelectionContext - at end', () => {
  const fullText = "Hello world";
  const result = extractSelectionContext(fullText, 6, 11, 3, 10);
  
  assertEqual(result.selectedText, "world");
  assertEqual(result.beforeText, "llo");
  assertEqual(result.afterText, "");
});

// Test prompt creation
test('createAutocompletePrompt - creates proper prompt', () => {
  const context = "The weather is";
  const systemPrompt = "Continue writing the following text";
  const result = createAutocompletePrompt(context, systemPrompt);

  if (!result.includes(context)) {
    throw new Error("Prompt should include the context");
  }
  if (!result.includes(systemPrompt)) {
    throw new Error("Prompt should include the system prompt");
  }
});

test('createAnswerSelectionPrompt - creates proper prompt', () => {
  const beforeText = "The cat sat";
  const afterText = "and meowed loudly";
  const systemPrompt = "Provide text that would naturally fit between segments";
  const result = createAnswerSelectionPrompt(beforeText, afterText, systemPrompt);

  if (!result.includes(beforeText)) {
    throw new Error("Prompt should include before text");
  }
  if (!result.includes(afterText)) {
    throw new Error("Prompt should include after text");
  }
  if (!result.includes(systemPrompt)) {
    throw new Error("Prompt should include the system prompt");
  }
});

console.log('\n🧪 Running text utility tests...\n');

// Run all tests
test('getTextContext - no max length', () => {
  const text = "Hello world";
  const result = getTextContext(text);
  assertEqual(result, "Hello world");
});

test('getTextContext - with max length', () => {
  const text = "Hello world this is a long text";
  const result = getTextContext(text, 10);
  assertEqual(result, " long text");
});

test('extractSelectionContext - basic case', () => {
  const fullText = "The quick brown fox jumps over the lazy dog";
  const result = extractSelectionContext(fullText, 10, 15, 5, 5);
  
  assertEqual(result.selectedText, "brown");
  assertEqual(result.beforeText, "quick");
  assertEqual(result.afterText, " fox ");
});

test('createAutocompletePrompt - creates proper prompt', () => {
  const context = "The weather is";
  const systemPrompt = "Continue writing the following text";
  const result = createAutocompletePrompt(context, systemPrompt);

  if (!result.includes(context)) {
    throw new Error("Prompt should include the context");
  }
  if (!result.includes(systemPrompt)) {
    throw new Error("Prompt should include the system prompt");
  }
});

test('createAutocompletePrompt - includes whitespace preservation instructions', () => {
  const context = "The weather is ";
  const systemPrompt = "Continue writing. Respect all whitespace including trailing spaces.";
  const result = createAutocompletePrompt(context, systemPrompt);

  if (!result.includes("Respect all whitespace")) {
    throw new Error("Prompt should include whitespace preservation instructions");
  }
  if (!result.includes("trailing spaces")) {
    throw new Error("Prompt should mention trailing spaces");
  }
});

test('createAnswerSelectionPrompt - creates proper prompt', () => {
  const beforeText = "The cat sat";
  const afterText = "and meowed loudly";
  const systemPrompt = "Provide text that fits between segments";
  const result = createAnswerSelectionPrompt(beforeText, afterText, systemPrompt);

  if (!result.includes(beforeText)) {
    throw new Error("Prompt should include before text");
  }
  if (!result.includes(afterText)) {
    throw new Error("Prompt should include after text");
  }
  if (!result.includes(systemPrompt)) {
    throw new Error("Prompt should include the system prompt");
  }
});

test('createAnswerSelectionPrompt - includes whitespace preservation instructions', () => {
  const beforeText = "The cat sat ";
  const afterText = " and meowed loudly";
  const systemPrompt = "Provide text. Respect all whitespace including trailing or leading spaces.";
  const result = createAnswerSelectionPrompt(beforeText, afterText, systemPrompt);

  if (!result.includes("Respect all whitespace")) {
    throw new Error("Prompt should include whitespace preservation instructions");
  }
  if (!result.includes("trailing or leading spaces")) {
    throw new Error("Prompt should mention trailing or leading spaces");
  }
});

test('extractCursorContext - basic case', () => {
  const fullText = "The quick brown fox jumps over the lazy dog";
  const result = extractCursorContext(fullText, 20, 10, 8);

  assertEqual(result.beforeText, "ick brown ");
  assertEqual(result.afterText, "jumps ov");
});

test('extractCursorContext - at beginning', () => {
  const fullText = "Hello world";
  const result = extractCursorContext(fullText, 0, 10, 5);

  assertEqual(result.beforeText, "");
  assertEqual(result.afterText, "Hello");
});

test('extractCursorContext - at end', () => {
  const fullText = "Hello world";
  const result = extractCursorContext(fullText, 11, 5, 10);

  assertEqual(result.beforeText, "world");
  assertEqual(result.afterText, "");
});

test('createCursorInsertPrompt - creates proper prompt', () => {
  const beforeText = "The cat";
  const afterText = "meowed loudly";
  const systemPrompt = "Provide text to insert at cursor position";
  const result = createCursorInsertPrompt(beforeText, afterText, systemPrompt);

  if (!result.includes(beforeText)) {
    throw new Error("Prompt should include before text");
  }
  if (!result.includes(afterText)) {
    throw new Error("Prompt should include after text");
  }
  if (!result.includes("cursor")) {
    throw new Error("Prompt should mention cursor position");
  }
  if (!result.includes(systemPrompt)) {
    throw new Error("Prompt should include the system prompt");
  }
});

test('createCursorInsertPrompt - includes whitespace preservation instructions', () => {
  const beforeText = "The cat ";
  const afterText = " meowed loudly";
  const systemPrompt = "Provide text. Respect all whitespace including trailing or leading spaces.";
  const result = createCursorInsertPrompt(beforeText, afterText, systemPrompt);

  if (!result.includes("Respect all whitespace")) {
    throw new Error("Prompt should include whitespace preservation instructions");
  }
  if (!result.includes("trailing or leading spaces")) {
    throw new Error("Prompt should mention trailing or leading spaces");
  }
});

// Test spacing preservation
test('extractSelectionContext - preserves spaces', () => {
  const fullText = "The   quick   brown   fox";
  const result = extractSelectionContext(fullText, 6, 11, 3, 3);

  assertEqual(result.selectedText, "quick");
  assertEqual(result.beforeText, "   ");
  assertEqual(result.afterText, "   ");
});

test('extractCursorContext - preserves spaces', () => {
  const fullText = "Hello   world   test";
  const result = extractCursorContext(fullText, 8, 5, 5);

  assertEqual(result.beforeText, "llo  ");
  assertEqual(result.afterText, "world");
});

console.log('\n✨ All tests completed!\n');
