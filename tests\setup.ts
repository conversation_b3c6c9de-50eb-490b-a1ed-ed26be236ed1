import { vi } from 'vitest';

// Mock DOM globals for Svelte 5
if (typeof globalThis.document === 'undefined') {
	Object.defineProperty(globalThis, 'document', {
		value: globalThis.window?.document || {},
		writable: true
	});
}

if (typeof globalThis.window === 'undefined') {
	Object.defineProperty(globalThis, 'window', {
		value: globalThis.window || {},
		writable: true
	});
}

// Create a proper localStorage mock that behaves like the real thing
class LocalStorageMock {
	private store: Record<string, string> = {};

	getItem(key: string): string | null {
		return this.store[key] || null;
	}

	setItem(key: string, value: string): void {
		this.store[key] = String(value);
	}

	removeItem(key: string): void {
		delete this.store[key];
	}

	clear(): void {
		this.store = {};
	}

	get length(): number {
		return Object.keys(this.store).length;
	}

	key(index: number): string | null {
		const keys = Object.keys(this.store);
		return keys[index] || null;
	}
}

// Set up localStorage mock
const localStorageMock = new LocalStorageMock();
Object.defineProperty(window, 'localStorage', {
	value: localStorageMock,
	writable: true
});

// Mock browser environment
vi.mock('$app/environment', () => ({
	browser: true
}));

// Reset localStorage before each test
import { beforeEach } from 'vitest';
beforeEach(() => {
	localStorageMock.clear();
});
