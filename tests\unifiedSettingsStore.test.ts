import { describe, it, expect, beforeEach, vi } from 'vitest';
import { get } from 'svelte/store';
import { apiKeys, updateApiKey, initializeAllSettings, setupAutoSave, settingsLoaded } from '$lib/stores/unifiedSettingsStore';
import { DEFAULT_API_KEYS } from '$lib/config';

// Mock the storage service
vi.mock('$lib/services/storageService', () => ({
	loadUnifiedSettings: vi.fn(),
	saveUnifiedSettings: vi.fn(),
	UNIFIED_SETTINGS_STORAGE_KEY: 'aiNotepadSvelteUnifiedSettings'
}));

import { loadUnifiedSettings, saveUnifiedSettings } from '$lib/services/storageService';

describe('Unified Settings Store', () => {
	beforeEach(() => {
		// Clear localStorage and reset mocks
		localStorage.clear();
		vi.clearAllMocks();

		// Reset store to default state
		apiKeys.set({ ...DEFAULT_API_KEYS });

		// Mock loadUnifiedSettings to return default values
		vi.mocked(loadUnifiedSettings).mockReturnValue({
			apiKeys: { ...DEFAULT_API_KEYS },
			appSettings: {
				fontFamily: 'Arial, sans-serif',
				fontSize: '16',
				aiProvider: 'openai',
				aiModel: 'gpt-4o-mini',
				autocompleteContextLength: 1000,
				sidebarVisible: true
			},
			notepadContent: '',
			customModelHistory: { gemini: [], openai: [], custom: [] },
			lastCustomModels: { gemini: '', openai: '', custom: '' },
			lastSelectedModels: {
				gemini: 'gemini-1.5-flash',
				openai: 'gpt-4o-mini',
				custom: ''
			}
		});

		// Mock saveUnifiedSettings to return true
		vi.mocked(saveUnifiedSettings).mockReturnValue(true);
	});

	describe('apiKeys store', () => {
		it('should initialize with default API keys', () => {
			const keys = get(apiKeys);
			expect(keys).toEqual(DEFAULT_API_KEYS);
		});

		it('should update API keys', () => {
			updateApiKey('openai', 'sk-test-key');

			const keys = get(apiKeys);
			expect(keys.openai).toBe('sk-test-key');
			expect(keys.gemini).toBe(''); // Should remain unchanged
		});

		it('should update multiple API keys independently', () => {
			updateApiKey('openai', 'sk-openai-key');
			updateApiKey('gemini', 'gemini-test-key');
			updateApiKey('customUrl', 'https://api.custom.com');
			updateApiKey('customKey', 'custom-api-key');

			const keys = get(apiKeys);
			expect(keys).toEqual({
				openai: 'sk-openai-key',
				gemini: 'gemini-test-key',
				customUrl: 'https://api.custom.com',
				customKey: 'custom-api-key'
			});
		});

		it('should handle empty string values', () => {
			// First set a value
			updateApiKey('openai', 'sk-test-key');

			// Then clear it
			updateApiKey('openai', '');

			const keys = get(apiKeys);
			expect(keys.openai).toBe('');
		});

		it('should preserve other keys when updating one key', () => {
			// Set initial values
			updateApiKey('openai', 'sk-openai-key');
			updateApiKey('gemini', 'gemini-key');

			// Update only one key
			updateApiKey('openai', 'sk-new-openai-key');

			const keys = get(apiKeys);
			expect(keys.openai).toBe('sk-new-openai-key');
			expect(keys.gemini).toBe('gemini-key'); // Should be preserved
			expect(keys.customUrl).toBe(''); // Should remain default
			expect(keys.customKey).toBe(''); // Should remain default
		});
	});

	describe('Store initialization', () => {
		it('should load settings from storage on initialization', async () => {
			const mockStoredSettings = {
				apiKeys: {
					openai: 'sk-stored-key',
					gemini: 'stored-gemini-key',
					customUrl: 'https://stored.api.com',
					customKey: 'stored-custom-key'
				},
				appSettings: {
					fontFamily: 'Arial, sans-serif',
					fontSize: '18',
					aiProvider: 'gemini' as const,
					aiModel: 'gemini-1.5-pro',
					autocompleteContextLength: 1500,
					sidebarVisible: true
				},
				notepadContent: 'Stored content',
				customModelHistory: { gemini: [], openai: [], custom: [] },
				lastCustomModels: { gemini: '', openai: '', custom: '' },
				lastSelectedModels: {
					gemini: 'gemini-1.5-pro',
					openai: 'gpt-4o',
					custom: ''
				},
				providerSpecificModels: {
					gemini: 'gemini-1.5-pro',
					openai: 'gpt-4o',
					custom: ''
				}
			};

			vi.mocked(loadUnifiedSettings).mockReturnValue(mockStoredSettings);
			
			// Simulate initialization
			await initializeAllSettings();
			
			const keys = get(apiKeys);
			expect(keys).toEqual(mockStoredSettings.apiKeys);
			expect(loadUnifiedSettings).toHaveBeenCalled();
		});

		it('should handle storage loading errors gracefully', async () => {
			vi.mocked(loadUnifiedSettings).mockImplementation(() => {
				throw new Error('Storage error');
			});

			// Should not throw and should use defaults
			await expect(initializeAllSettings()).resolves.not.toThrow();
		});
	});


});
